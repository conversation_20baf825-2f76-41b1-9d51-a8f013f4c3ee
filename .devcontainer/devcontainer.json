{"name": "RSGlider API Development", "dockerComposeFile": ["../docker-compose.yml", "docker-compose.override.yml"], "service": "api", "workspaceFolder": "/app", "shutdownAction": "stopCompose", "features": {"ghcr.io/devcontainers/features/git:1": {}}, "customizations": {"vscode": {"extensions": ["vscode.typescript-language-features", "ms-vscode-remote.remote-containers", "redhat.vscode-yaml"], "settings": {"typescript.preferences.includePackageJsonAutoImports": "auto", "typescript.suggest.autoImports": true, "editor.formatOnSave": true, "files.watcherExclude": {"**/node_modules/**": true, "**/dist/**": true, "**/.git/**": true}, "terminal.integrated.shell.linux": "/bin/sh"}}}, "forwardPorts": [3000, 5432, 6379, 9000, 3001, 8080, 9229], "portsAttributes": {"3000": {"label": "API Server", "onAutoForward": "notify"}, "3001": {"label": "<PERSON><PERSON><PERSON>", "onAutoForward": "silent"}, "5432": {"label": "PostgreSQL", "onAutoForward": "silent"}, "6379": {"label": "Redis", "onAutoForward": "silent"}, "9000": {"label": "MinIO", "onAutoForward": "silent"}, "8080": {"label": "Redis Commander", "onAutoForward": "silent"}, "9229": {"label": "Debug Port", "onAutoForward": "silent"}}, "postCreateCommand": "npm install", "containerEnv": {"NODE_ENV": "development", "CHOKIDAR_USEPOLLING": "true", "WATCHPACK_POLLING": "true"}, "remoteUser": "node", "overrideCommand": false}