version: '3.8'

services:
  api:
    # Override the default command to keep container running for devcontainer
    command: sleep infinity
    
    # Mount source code for hot reload
    volumes:
      - ..:/app:cached
      - /app/node_modules
    
    # Environment variables for development
    environment:
      - NODE_ENV=development
      - CHOKIDAR_USEPOLLING=true
      - WATCHPACK_POLLING=true
      - DEBUG=*
    
    # Run as node user for better file permissions
    user: node
    
    # Keep stdin open and allocate a pseudo-TTY
    stdin_open: true
    tty: true
