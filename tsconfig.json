{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"@/*": ["src/*"], "@/auth/*": ["src/auth/*"], "@/users/*": ["src/users/*"], "@/admin/*": ["src/admin/*"], "@/store/*": ["src/store/*"], "@/developer/*": ["src/developer/*"], "@/webhooks/*": ["src/webhooks/*"], "@/common/*": ["src/common/*"], "@/config/*": ["src/config/*"], "@/database/*": ["src/database/*"]}}, "include": ["src/**/*", "scripts/**/*"], "exclude": ["node_modules", "dist"]}