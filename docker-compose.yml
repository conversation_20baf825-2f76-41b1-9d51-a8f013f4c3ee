services:
  # PostgreSQL Database
  postgres:
    image: postgres:17-alpine
    container_name: rsglider-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: rsglider
      POSTGRES_USER: rsglider
      POSTGRES_PASSWORD: rsglider_dev_password
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - rsglider-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U rsglider -d rsglider"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for Sessions
  redis:
    image: redis:7-alpine
    container_name: rsglider-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass rsglider_redis_password
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "6379:6379"
    networks:
      - rsglider-network
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "rsglider_redis_password", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # RSGlider API Application
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: rsglider-api
    restart: unless-stopped
    environment:
      NODE_ENV: development
      PORT: 3000
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      DATABASE_NAME: rsglider
      DATABASE_USER: rsglider
      DATABASE_PASSWORD: rsglider_dev_password
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: rsglider_redis_password
      JWT_SECRET: dev_jwt_secret_change_in_production
      JWT_EXPIRES_IN: 15m
      REFRESH_TOKEN_EXPIRES_IN: 7d
      BTCPAY_SERVER_URL: https://btcpay.rsglider.com
      BTCPAY_STORE_ID: dev_store_id
      BTCPAY_API_KEY: dev_api_key
      BTCPAY_WEBHOOK_SECRET: dev_webhook_secret
      S3_ENDPOINT: http://minio:9000
      S3_ACCESS_KEY: rsglider_minio_user
      S3_SECRET_KEY: rsglider_minio_password
      S3_BUCKET_NAME: rsglider-uploads
      S3_REGION: us-east-1
      S3_FORCE_PATH_STYLE: true
      GITEA_BASE_URL: http://gitea:3000
      GITEA_EXTERNAL_URL: http://localhost:3001
      GITEA_ADMIN_TOKEN: rsglider_gitea_admin_token_change_in_production
      GITEA_WEBHOOK_SECRET: rsglider_gitea_webhook_secret_change_in_production
    volumes:
      # Mount source code for hot reload, but exclude node_modules
      - ./src:/app/src
      - ./package.json:/app/package.json
      - ./package-lock.json:/app/package-lock.json
      - ./tsconfig.json:/app/tsconfig.json
      - ./nest-cli.json:/app/nest-cli.json
      - ./drizzle.config.ts:/app/drizzle.config.ts
      - api_uploads:/app/uploads
      # Use named volume for node_modules to avoid architecture conflicts
      - api_node_modules:/app/node_modules
    ports:
      - "3000:3000"
      - "9229:9229" # Debug port
    networks:
      - rsglider-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
      gitea:
        condition: service_healthy
    command: npm run start:dev

  # MinIO Object Storage (S3 Compatible)
  minio:
    image: minio/minio:latest
    container_name: rsglider-minio
    restart: unless-stopped
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: rsglider_minio_user
      MINIO_ROOT_PASSWORD: rsglider_minio_password
      MINIO_BROWSER_REDIRECT_URL: http://localhost:9002
    volumes:
      - minio_data:/data
    ports:
      - "9000:9000"  # MinIO API
      - "9002:9001"  # MinIO Console
    networks:
      - rsglider-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # Gitea Git Service
  gitea:
    image: docker.gitea.com/gitea:1.23.8
    container_name: rsglider-gitea
    restart: always
    environment:
      - USER_UID=1000
      - USER_GID=1000
      - GITEA__database__DB_TYPE=postgres
      - GITEA__database__HOST=postgres:5432
      - GITEA__database__NAME=rsglider_gitea
      - GITEA__database__USER=rsglider
      - GITEA__database__PASSWD=rsglider_dev_password
    volumes:
      - gitea_data:/data
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    ports:
      - "3001:3000"  # Gitea Web UI
      - "2222:22"    # Gitea SSH
    networks:
      - rsglider-network
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/healthz"]
      interval: 30s
      timeout: 20s
      retries: 3

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local
  gitea_data:
    driver: local
  api_uploads:
    driver: local
  api_node_modules:
    driver: local

networks:
  rsglider-network:
    driver: bridge
