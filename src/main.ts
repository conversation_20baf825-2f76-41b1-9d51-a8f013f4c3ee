import { Logger, ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import * as session from 'express-session';
import * as passport from 'passport';
import { AppModule } from './app.module';
import { RedisService } from './common/services/redis.service';
import { S3Service } from './common/services/s3.service';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);
  const logger = new Logger('Bootstrap');

  // Initialize S3 service and buckets
  try {
    const s3Service = app.get(S3Service);
    await s3Service.initializeBuckets();
    logger.log('✅ S3 buckets initialized successfully');
  } catch (error) {
    logger.error('❌ Failed to initialize S3 buckets', error);
    // Don't exit - let the app start but log the error
  }

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: false, // Disable implicit conversion to enforce strict validation
      },
    }),
  );

  // CORS configuration
  app.enableCors({
    origin: configService.get('CORS_ORIGIN', '*').split(','),
    credentials: configService.get('CORS_CREDENTIALS', 'true') === 'true',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: [
      'Origin',
      'X-Requested-With',
      'Content-Type',
      'Accept',
      'Authorization',
      'X-API-Key',
    ],
  });

  // Configure Redis session store
  try {
    const redisService = app.get(RedisService);
    const redisClient = redisService.getClient();

    // Initialize RedisStore (connect-redis v7 syntax)
    const redisStore = new RedisStore({
      client: redisClient,
      prefix: 'rsglider:sess:',
    });

    app.use(
      session({
        store: redisStore,
        secret: configService.get('SESSION_SECRET', 'dev-secret'),
        resave: false,
        saveUninitialized: false,
        cookie: {
          maxAge: parseInt(configService.get('SESSION_MAX_AGE', '86400000')),
          httpOnly: true,
          secure: configService.get('NODE_ENV') === 'production',
        },
      }),
    );

    logger.log('✅ Redis session store configured successfully');
  } catch (error) {
    logger.error('❌ Failed to configure Redis session store, falling back to memory store', error);

    // Fallback to memory store
    app.use(
      session({
        secret: configService.get('SESSION_SECRET', 'dev-secret'),
        resave: false,
        saveUninitialized: false,
        cookie: {
          maxAge: parseInt(configService.get('SESSION_MAX_AGE', '86400000')),
          httpOnly: true,
          secure: configService.get('NODE_ENV') === 'production',
        },
      }),
    );
  }

  // Passport initialization
  app.use(passport.initialize());
  app.use(passport.session());

  // Swagger configuration
  const config = new DocumentBuilder()
    .setTitle('RSGlider API')
    .setDescription('The RSGlider API for desktop and web applications')
    .setVersion('1.0.0')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter JWT token',
        in: 'header',
      },
      'JWT-auth',
    )
    .addApiKey(
      {
        type: 'apiKey',
        name: 'X-API-Key',
        in: 'header',
        description: 'API Key for service-to-service communication',
      },
      'API-Key',
    )
    .addTag('Authentication', 'User authentication and session management')
    .addTag('User Profile', 'User profile and account management')
    .addTag('Admin', 'Administrative operations')
    .addTag('Store', 'Marketplace and store operations')
    .addTag('Developer', 'Developer tools and repository management')
    .addTag('Webhooks', 'Webhook endpoints for external integrations')
    .addTag('Client Updates', 'Desktop client update management')
    .addTag('File Uploads', 'File upload and storage management')
    .build();

  const documentFactory = () => SwaggerModule.createDocument(app, config, {
    operationIdFactory: (_controllerKey: string, methodKey: string) => methodKey,
    deepScanRoutes: true,
  });

  SwaggerModule.setup('api/docs', app, documentFactory, {
    customSiteTitle: 'RSGlider API Documentation',
    customfavIcon: '/favicon.ico',
    customCss: '.swagger-ui .topbar { display: none }',
    swaggerOptions: {
      persistAuthorization: true,
      displayRequestDuration: true,
    },
  });

  // Global prefix
  app.setGlobalPrefix('api', {
    exclude: ['/health', '/'],
  });

  // Health check endpoint
  app.use('/health', (_req, res) => {
    res.status(200).json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: configService.get('NODE_ENV'),
    });
  });

  const port = configService.get('PORT', 3000);
  await app.listen(port);

  logger.log(`🚀 Application is running on: http://localhost:${port}`);
  logger.log(`📚 API Documentation: http://localhost:${port}/api/docs`);
  logger.log(`🏥 Health Check: http://localhost:${port}/health`);
}

bootstrap().catch((error) => {
  console.error('Failed to start application:', error);
  process.exit(1);
});
