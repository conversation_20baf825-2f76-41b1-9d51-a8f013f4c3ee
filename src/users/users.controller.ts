import { CurrentUser, CurrentUserData } from '@/common/decorators/current-user.decorator';
import { JwtAuthGuard } from '@/common/guards/jwt-auth.guard';
import { Body, Controller, Delete, ForbiddenException, Get, Param, Post, Put, Req, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { CreateDeviceRequestDto } from './dto/create-device-request.dto';
import { DeviceDto } from './dto/device.dto';
import { SessionDto } from './dto/session.dto';
import { TwoFactorDisableRequest } from './dto/two-factor-disable-request.dto';
import { TwoFactorEnabledResponse } from './dto/two-factor-enabled-response.dto';
import { TwoFactorSetupResponse } from './dto/two-factor-setup-response.dto';
import { TwoFactorVerifyRequest } from './dto/two-factor-verify-request.dto';
import { UpdateUserRequest } from './dto/update-user-request.dto';
import { User } from './dto/user.dto';
import { UsersService } from './users.service';

@ApiTags('User Profile')
@Controller('users')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get('/me')
  @ApiOperation({ summary: 'Get current user profile' })
  @ApiResponse({ status: 200, description: 'Success', type: User })
  async getCurrentUser(@CurrentUser() user: CurrentUserData): Promise<User> {
    return this.usersService.getCurrentUser(user.id);
  }

  @Put('/me')
  @ApiOperation({ summary: 'Update current user profile' })
  @ApiResponse({ status: 200, description: 'Success', type: User })
  async updateCurrentUser(
    @CurrentUser() user: CurrentUserData,
    @Body() updateData: UpdateUserRequest
  ): Promise<User> {
    return this.usersService.updateCurrentUser(user.id, updateData);
  }

  // 2FA Management Endpoints

  @Post('/me/2fa/setup')
  @ApiOperation({ summary: 'Setup two-factor authentication' })
  @ApiResponse({ status: 200, description: '2FA setup initiated', type: TwoFactorSetupResponse })
  @ApiResponse({ status: 400, description: '2FA already enabled' })
  async setup2FA(@CurrentUser() user: CurrentUserData): Promise<TwoFactorSetupResponse> {
    return this.usersService.setup2FA(user.id);
  }

  @Post('/me/2fa/verify-setup')
  @ApiOperation({ summary: 'Verify and enable two-factor authentication' })
  @ApiResponse({ status: 200, description: '2FA enabled successfully', type: TwoFactorEnabledResponse })
  @ApiResponse({ status: 400, description: 'Invalid TOTP code or setup not initiated' })
  async verifySetup2FA(
    @CurrentUser() user: CurrentUserData,
    @Body() verifyData: TwoFactorVerifyRequest
  ): Promise<TwoFactorEnabledResponse> {
    return this.usersService.verifySetup2FA(user.id, verifyData.code);
  }

  @Post('/me/2fa/disable')
  @ApiOperation({ summary: 'Disable two-factor authentication' })
  @ApiResponse({ status: 200, description: '2FA disabled successfully' })
  @ApiResponse({ status: 400, description: 'Invalid credentials or verification code' })
  async disable2FA(
    @CurrentUser() user: CurrentUserData,
    @Body() disableData: TwoFactorDisableRequest
  ): Promise<{ message: string }> {
    return this.usersService.disable2FA(user.id, disableData.password, disableData.code);
  }

  // Device Management Endpoints

  @Get('/me/devices')
  @ApiOperation({ summary: 'Get registered devices' })
  @ApiResponse({ status: 200, description: 'List of devices', type: [DeviceDto] })
  async getDevices(@CurrentUser() user: CurrentUserData): Promise<DeviceDto[]> {
    return this.usersService.getUserDevices(user.id);
  }

  @Post('/me/devices')
  @ApiOperation({ summary: 'Register new device' })
  @ApiResponse({ status: 201, description: 'Device registered', type: DeviceDto })
  async registerDevice(
    @CurrentUser() user: CurrentUserData,
    @Body() dto: CreateDeviceRequestDto
  ): Promise<DeviceDto> {
    return this.usersService.registerDevice(user.id, dto);
  }

  @Delete('/me/devices/:deviceId')
  @ApiOperation({ summary: 'Remove device' })
  @ApiResponse({ status: 200, description: 'Device removed' })
  async removeDevice(
    @CurrentUser() user: CurrentUserData,
    @Param('deviceId') deviceId: string
  ): Promise<{ message: string }> {
    return this.usersService.removeDevice(user.id, deviceId);
  }

  // Session Management Endpoints

  @Get('/me/sessions')
  @ApiOperation({ summary: 'Get active sessions' })
  @ApiResponse({ status: 200, description: 'List of sessions', type: [SessionDto] })
  async getSessions(@CurrentUser() user: CurrentUserData): Promise<SessionDto[]> {
    return this.usersService.getUserSessions(user.id);
  }

  @Delete('/me/sessions/:sessionId')
  @ApiOperation({ summary: 'Remove a session' })
  @ApiResponse({ status: 200, description: 'Session removed' })
  async removeSession(
    @CurrentUser() user: CurrentUserData,
    @Param('sessionId') sessionId: string,
    @Req() req: Request
  ): Promise<{ message: string }> {
    if (!user || (!user.roles?.includes('admin') && !user.id)) {
      throw new ForbiddenException('Not authorized');
    }
    return this.usersService.removeUserSession(user.id, sessionId, req);
  }

  @Delete('/me/sessions')
  @ApiOperation({ summary: 'Remove all sessions except current' })
  @ApiResponse({ status: 200, description: 'All other sessions removed' })
  async removeAllSessions(@CurrentUser() user: CurrentUserData, @Req() req: Request): Promise<{ message: string }> {
    if (!user || (!user.roles?.includes('admin') && !user.id)) {
      throw new ForbiddenException('Not authorized');
    }
    return this.usersService.removeAllUserSessions(user.id, req);
  }
}
