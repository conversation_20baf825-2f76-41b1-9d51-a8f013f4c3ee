import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEmail, IsBoolean, IsNumber, IsArray } from 'class-validator';

export class Role {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  id?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  permissions?: any[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isSystemRole?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  userCount?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  createdAt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  updatedAt?: string;

}
