import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { drizzle } from 'drizzle-orm/postgres-js';
import * as schema from './schema';
import postgres = require('postgres');

@Module({
  providers: [
    {
      provide: 'DB',
      useFactory: (configService: ConfigService) => {
        const connectionString = `postgresql://${configService.get('DATABASE_USER', 'rsglider')}:${configService.get('DATABASE_PASSWORD', 'rsglider_dev_password')}@${configService.get('DATABASE_HOST', 'postgres')}:${configService.get('DATABASE_PORT', '5432')}/${configService.get('DATABASE_NAME', 'rsglider')}`;

        console.log('🔗 Database connection string:', connectionString.replace(/:[^:@]*@/, ':***@'));

        const client = postgres(connectionString, {
          max: 10,
          idle_timeout: 20,
          connect_timeout: 10,
          onnotice: (notice) => console.log('🔔 PostgreSQL notice:', notice),
          debug: (__connection, query, params) => {
            console.log('🐛 SQL Debug:', { query, params });
          },
        });

        // Test the connection
        client`SELECT 1 as test`.then(
          (result) => console.log('✅ Database connection test successful:', result),
          (error) => console.error('❌ Database connection test failed:', error)
        );

        return drizzle(client, { schema });
      },
      inject: [ConfigService],
    },
  ],
  exports: ['DB'],
})
export class DatabaseModule {}
