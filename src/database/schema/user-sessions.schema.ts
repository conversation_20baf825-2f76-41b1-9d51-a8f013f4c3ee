import { relations } from 'drizzle-orm';
import { boolean, index, json, pgEnum, pgTable, timestamp, uuid, varchar } from 'drizzle-orm/pg-core';
import { users } from './users.schema';

// Enum for session platforms
export const sessionPlatformEnum = pgEnum('session_platform', ['web', 'desktop', 'bot']);

export const userSessions = pgTable('user_sessions', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').notNull().references(() => users.id, { onDelete: 'cascade' }),
  platform: sessionPlatformEnum('platform').notNull().default('web'),
  deviceInfo: json('device_info').$type<{
    userAgent?: string;
    browser?: string;
    os?: string;
    deviceName?: string;
    trusted?: boolean;
  }>(),
  location: json('location').$type<{
    ipAddress?: string;
    country?: string;
    city?: string;
    region?: string;
  }>(),
  expiresAt: timestamp('expires_at').notNull(),
  isActive: boolean('is_active').notNull().default(true),
  isCurrent: boolean('is_current').notNull().default(false),
  requiresVerification: boolean('requires_verification').notNull().default(false),
  lastActivityAt: timestamp('last_activity_at'),
  revokedAt: timestamp('revoked_at'),
  revokedByIp: varchar('revoked_by_ip', { length: 45 }),
  revokedReason: varchar('revoked_reason', { length: 255 }),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow().$onUpdate(() => new Date()),
}, (table) => ({
  userIdIdx: index('user_sessions_user_id_idx').on(table.userId),
}));

// Relations
export const userSessionsRelations = relations(userSessions, ({ one }) => ({
  user: one(users, {
    fields: [userSessions.userId],
    references: [users.id],
  }),
}));

// Types
export type UserSession = typeof userSessions.$inferSelect;
export type NewUserSession = typeof userSessions.$inferInsert;
export type SessionPlatform = typeof userSessions.platform.enumValues[number];
