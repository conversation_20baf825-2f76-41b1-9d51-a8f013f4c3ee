// Export all schemas
export * from './client-releases.schema';
export * from './devices.schema';
export * from './file-uploads.schema';
export * from './gitea-profiles.schema';
export * from './gitea-repositories.schema';
export * from './marketplace-items.schema';
export * from './permissions.schema';
export * from './refresh-tokens.schema';
export * from './role-permissions.schema';
export * from './roles.schema';
export * from './user-roles.schema';
export * from './user-sessions.schema';
export * from './users.schema';

// Export relations
import { devicesRelations } from './devices.schema';
import { fileUploadsRelations } from './file-uploads.schema';
import { giteaProfilesRelations } from './gitea-profiles.schema';
import { giteaRepositoriesRelations } from './gitea-repositories.schema';
import { marketplaceItemsRelations } from './marketplace-items.schema';
import { refreshTokensRelations } from './refresh-tokens.schema';
import { userSessionsRelations } from './user-sessions.schema';

export const relations = {
  devicesRelations,
  fileUploadsRelations,
  refreshTokensRelations,
  userSessionsRelations,
  giteaProfilesRelations,
  giteaRepositoriesRelations,
  marketplaceItemsRelations,
};
