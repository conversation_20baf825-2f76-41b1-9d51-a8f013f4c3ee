import { relations } from 'drizzle-orm';
import { boolean, decimal, index, json, pgEnum, pgTable, timestamp, uuid, varchar } from 'drizzle-orm/pg-core';
import { users } from './users.schema';

// Enum for file types
export const fileTypeEnum = pgEnum('file_type', ['image', 'video', 'document', 'archive', 'executable']);

// Enum for upload status
export const uploadStatusEnum = pgEnum('upload_status', ['pending', 'uploading', 'completed', 'failed', 'deleted']);

export const fileUploads = pgTable('file_uploads', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').references(() => users.id, { onDelete: 'cascade' }),

  // File metadata
  originalName: varchar('original_name', { length: 255 }).notNull(),
  fileName: varchar('file_name', { length: 255 }).notNull(), // Generated unique filename
  mimeType: varchar('mime_type', { length: 100 }).notNull(),
  fileType: fileTypeEnum('file_type').notNull(),
  fileSize: decimal('file_size', { precision: 15, scale: 0 }).notNull(), // Size in bytes

  // S3 storage information
  s3Key: varchar('s3_key', { length: 500 }).notNull(),
  s3Bucket: varchar('s3_bucket', { length: 100 }).notNull(),
  s3ETag: varchar('s3_etag', { length: 100 }), // S3 ETag for integrity verification

  // Upload tracking
  status: uploadStatusEnum('status').notNull().default('pending'),
  uploadProgress: decimal('upload_progress', { precision: 5, scale: 2 }).default('0'), // Percentage 0-100

  // File validation
  isValidated: boolean('is_validated').notNull().default(false),
  validationErrors: json('validation_errors').$type<string[]>(),

  // Access control
  isPublic: boolean('is_public').notNull().default(false),
  accessToken: varchar('access_token', { length: 64 }), // For secure access to private files

  // Metadata for images/videos
  metadata: json('metadata').$type<{
    width?: number;
    height?: number;
    duration?: number; // For videos
    format?: string;
    bitrate?: number;
    fps?: number;
    thumbnailS3Key?: string; // For video thumbnails
  }>(),

  // Expiration and cleanup
  expiresAt: timestamp('expires_at'), // For temporary files
  deletedAt: timestamp('deleted_at'),

  // Timestamps
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow().$onUpdate(() => new Date()),
}, (table) => ({
  userIdIdx: index('file_uploads_user_id_idx').on(table.userId),
  statusIdx: index('file_uploads_status_idx').on(table.status),
  fileTypeIdx: index('file_uploads_file_type_idx').on(table.fileType),
  s3KeyIdx: index('file_uploads_s3_key_idx').on(table.s3Key),
  accessTokenIdx: index('file_uploads_access_token_idx').on(table.accessToken),
  expiresAtIdx: index('file_uploads_expires_at_idx').on(table.expiresAt),
}));

// Relations
export const fileUploadsRelations = relations(fileUploads, ({ one }) => ({
  user: one(users, {
    fields: [fileUploads.userId],
    references: [users.id],
  }),
}));

export type FileUpload = typeof fileUploads.$inferSelect;
export type NewFileUpload = typeof fileUploads.$inferInsert;
