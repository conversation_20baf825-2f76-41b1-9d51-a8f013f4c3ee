import { relations } from 'drizzle-orm';
import { boolean, decimal, index, integer, json, pgEnum, pgTable, text, timestamp, uuid, varchar } from 'drizzle-orm/pg-core';
import { giteaRepositories } from './gitea-repositories.schema';
import { users } from './users.schema';

// Enum for marketplace item status
export const marketplaceItemStatusEnum = pgEnum('marketplace_item_status', [
  'draft',
  'pending_review',
  'approved',
  'rejected',
  'published',
  'suspended',
  'archived'
]);

// Enum for pricing types
export const pricingTypeEnum = pgEnum('pricing_type', [
  'free',
  'one_time',
  'subscription'
]);

// Enum for marketplace categories
export const marketplaceCategoryEnum = pgEnum('marketplace_category', [
  'automation',
  'productivity',
  'utilities',
  'games',
  'development',
  'security',
  'finance',
  'social',
  'entertainment',
  'education',
  'other'
]);

export const marketplaceItems = pgTable('marketplace_items', {
  id: uuid('id').primaryKey().defaultRandom(),
  developerId: uuid('developer_id').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  giteaRepositoryId: uuid('gitea_repository_id').references(() => giteaRepositories.id, { onDelete: 'cascade' }),

  // Basic information
  name: varchar('name', { length: 255 }).notNull(),
  slug: varchar('slug', { length: 255 }).notNull().unique(),
  description: text('description').notNull(),
  shortDescription: varchar('short_description', { length: 500 }),

  // Categorization
  category: marketplaceCategoryEnum('category').notNull(),
  tags: json('tags').$type<string[]>(),

  // Pricing
  pricingType: pricingTypeEnum('pricing_type').notNull(),
  basePrice: decimal('base_price', { precision: 10, scale: 2 }),
  currency: varchar('currency', { length: 3 }).default('USD'),

  // Status and visibility
  status: marketplaceItemStatusEnum('status').notNull().default('draft'),
  visibility: varchar('visibility', { length: 20 }).notNull().default('public'), // public, private
  isFeatured: boolean('is_featured').notNull().default(false),
  isActive: boolean('is_active').notNull().default(true),

  // Content
  readme: text('readme'), // Markdown content
  changelog: text('changelog'), // Markdown content
  license: varchar('license', { length: 100 }),
  version: varchar('version', { length: 50 }),

  // Media
  iconUrl: text('icon_url'),
  screenshots: json('screenshots').$type<string[]>(),
  videoUrl: text('video_url'),

  // Links
  homepageUrl: text('homepage_url'),
  documentationUrl: text('documentation_url'),
  supportUrl: text('support_url'),

  // Requirements
  requirements: json('requirements').$type<{
    rsgliderVersion?: string;
    dependencies?: string[];
    platforms?: string[];
    minimumRam?: string;
    minimumStorage?: string;
  }>(),

  // Statistics
  downloadCount: integer('download_count').default(0),
  viewCount: integer('view_count').default(0),
  ratingAverage: decimal('rating_average', { precision: 3, scale: 2 }).default('0'),
  ratingCount: integer('rating_count').default(0),

  // Revenue tracking
  totalRevenue: decimal('total_revenue', { precision: 15, scale: 2 }).default('0'),
  totalSales: integer('total_sales').default(0),

  // SEO and metadata
  metaTitle: varchar('meta_title', { length: 255 }),
  metaDescription: varchar('meta_description', { length: 500 }),
  metaKeywords: json('meta_keywords').$type<string[]>(),

  // Admin fields
  reviewNotes: text('review_notes'),
  rejectionReason: text('rejection_reason'),
  reviewedBy: uuid('reviewed_by').references(() => users.id),
  reviewedAt: timestamp('reviewed_at'),

  // Publishing
  publishedAt: timestamp('published_at'),
  lastUpdatedAt: timestamp('last_updated_at'),

  // Timestamps
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow().$onUpdate(() => new Date()),
}, (table) => ({
  developerIdIdx: index('marketplace_items_developer_id_idx').on(table.developerId),
  giteaRepositoryIdIdx: index('marketplace_items_gitea_repository_id_idx').on(table.giteaRepositoryId),
  slugIdx: index('marketplace_items_slug_idx').on(table.slug),
  categoryIdx: index('marketplace_items_category_idx').on(table.category),
  statusIdx: index('marketplace_items_status_idx').on(table.status),
  visibilityIdx: index('marketplace_items_visibility_idx').on(table.visibility),
  isFeaturedIdx: index('marketplace_items_is_featured_idx').on(table.isFeatured),
  isActiveIdx: index('marketplace_items_is_active_idx').on(table.isActive),
  pricingTypeIdx: index('marketplace_items_pricing_type_idx').on(table.pricingType),
  publishedAtIdx: index('marketplace_items_published_at_idx').on(table.publishedAt),
}));

// Relations
export const marketplaceItemsRelations = relations(marketplaceItems, ({ one }) => ({
  developer: one(users, {
    fields: [marketplaceItems.developerId],
    references: [users.id],
  }),
  giteaRepository: one(giteaRepositories, {
    fields: [marketplaceItems.giteaRepositoryId],
    references: [giteaRepositories.id],
  }),
  reviewer: one(users, {
    fields: [marketplaceItems.reviewedBy],
    references: [users.id],
  }),
}));

export type MarketplaceItem = typeof marketplaceItems.$inferSelect;
export type NewMarketplaceItem = typeof marketplaceItems.$inferInsert;
