import { relations } from 'drizzle-orm';
import { boolean, integer, json, pgEnum, pgTable, text, timestamp, uuid, varchar } from 'drizzle-orm/pg-core';
import { giteaProfiles } from './gitea-profiles.schema';

// Enum for repository visibility
export const repositoryVisibilityEnum = pgEnum('repository_visibility', ['public', 'private']);

// Enum for repository sync status
export const repositorySyncStatusEnum = pgEnum('repository_sync_status', ['pending', 'syncing', 'completed', 'failed']);

export const giteaRepositories = pgTable('gitea_repositories', {
  id: uuid('id').primaryKey().defaultRandom(),
  giteaProfileId: uuid('gitea_profile_id').references(() => giteaProfiles.id, { onDelete: 'cascade' }).notNull(),

  // Gitea repository information
  giteaRepoId: integer('gitea_repo_id').notNull(),
  name: varchar('name', { length: 255 }).notNull(),
  fullName: varchar('full_name', { length: 500 }).notNull(), // owner/repo
  description: text('description'),

  // Repository properties
  visibility: repositoryVisibilityEnum('visibility').notNull(),
  isFork: boolean('is_fork').notNull().default(false),
  isTemplate: boolean('is_template').notNull().default(false),
  isArchived: boolean('is_archived').notNull().default(false),
  isEmpty: boolean('is_empty').notNull().default(false),

  // Repository statistics
  size: integer('size').default(0), // Size in KB
  starsCount: integer('stars_count').default(0),
  forksCount: integer('forks_count').default(0),
  watchersCount: integer('watchers_count').default(0),
  openIssuesCount: integer('open_issues_count').default(0),

  // Repository metadata
  defaultBranch: varchar('default_branch', { length: 100 }).default('main'),
  language: varchar('language', { length: 100 }),
  topics: json('topics').$type<string[]>(),

  // URLs
  htmlUrl: text('html_url').notNull(),
  cloneUrl: text('clone_url').notNull(),
  sshUrl: text('ssh_url').notNull(),

  // Marketplace integration
  isPublished: boolean('is_published').notNull().default(false),
  marketplaceItemId: uuid('marketplace_item_id'), // Reference to marketplace items table
  hasMarketplaceMetadata: boolean('has_marketplace_metadata').notNull().default(false),

  // Marketplace metadata (from marketplace.yml file)
  marketplaceMetadata: json('marketplace_metadata').$type<{
    name?: string;
    description?: string;
    category?: string;
    tags?: string[];
    version?: string;
    author?: string;
    license?: string;
    homepage?: string;
    documentation?: string;
    screenshots?: string[];
    pricing?: {
      type: 'free' | 'one_time' | 'subscription';
      basePrice?: number;
      currency?: string;
    };
    requirements?: {
      rsgliderVersion?: string;
      dependencies?: string[];
      platforms?: string[];
    };
  }>(),

  // Sync information
  lastSyncAt: timestamp('last_sync_at'),
  syncStatus: repositorySyncStatusEnum('sync_status').default('pending'),
  syncErrors: json('sync_errors').$type<string[]>(),

  // Gitea timestamps
  giteaCreatedAt: timestamp('gitea_created_at'),
  giteaUpdatedAt: timestamp('gitea_updated_at'),
  giteaPushedAt: timestamp('gitea_pushed_at'),

  // Local timestamps
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow().$onUpdate(() => new Date()),
}, (table) => ({
  giteaProfileIdIdx: index('gitea_repositories_gitea_profile_id_idx').on(table.giteaProfileId),
  giteaRepoIdIdx: index('gitea_repositories_gitea_repo_id_idx').on(table.giteaRepoId),
  fullNameIdx: index('gitea_repositories_full_name_idx').on(table.fullName),
  visibilityIdx: index('gitea_repositories_visibility_idx').on(table.visibility),
  isPublishedIdx: index('gitea_repositories_is_published_idx').on(table.isPublished),
  marketplaceItemIdIdx: index('gitea_repositories_marketplace_item_id_idx').on(table.marketplaceItemId),
  syncStatusIdx: index('gitea_repositories_sync_status_idx').on(table.syncStatus),
  languageIdx: index('gitea_repositories_language_idx').on(table.language),
  // Unique constraint for Gitea repo ID per profile
  uniqueGiteaRepo: index('gitea_repositories_unique_gitea_repo_idx').on(table.giteaProfileId, table.giteaRepoId),
}));

// Relations
export const giteaRepositoriesRelations = relations(giteaRepositories, ({ one }) => ({
  giteaProfile: one(giteaProfiles, {
    fields: [giteaRepositories.giteaProfileId],
    references: [giteaProfiles.id],
  }),
}));

export type GiteaRepository = typeof giteaRepositories.$inferSelect;
export type NewGiteaRepository = typeof giteaRepositories.$inferInsert;
