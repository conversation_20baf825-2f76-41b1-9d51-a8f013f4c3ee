import { permissions, rolePermissions, roles } from '@/database/schema';
import { BadRequestException, Inject, Injectable, NotFoundException } from '@nestjs/common';
import { and, eq } from 'drizzle-orm';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { CreateRoleRequest } from '../users/dto/create-role-request.dto';
import { Role } from '../users/dto/role.dto';
import { UpdateRoleRequest } from '../users/dto/update-role-request.dto';

@Injectable()
export class RolesService {
  constructor(@Inject('DB') private readonly db: PostgresJsDatabase<typeof import('@/database/schema')>) {}

  async listRoles(): Promise<Role[]> {
    const dbRoles = await this.db.select().from(roles);
    return dbRoles.map((r) => ({
      id: r.id,
      name: r.name,
      description: r.description,
      isSystemRole: r.isSystemRole,
      createdAt: r.createdAt?.toISOString(),
      updatedAt: r.updatedAt?.toISOString(),
    }));
  }

  async getRole(roleId: string): Promise<Role> {
    const [role] = await this.db.select().from(roles).where(eq(roles.id, roleId)).limit(1);
    if (!role) throw new NotFoundException('Role not found');
    return {
      id: role.id,
      name: role.name,
      description: role.description,
      isSystemRole: role.isSystemRole,
      createdAt: role.createdAt?.toISOString(),
      updatedAt: role.updatedAt?.toISOString(),
    };
  }

  async createRole(data: CreateRoleRequest): Promise<Role> {
    // Name must be unique
    const existing = await this.db.select().from(roles).where(eq(roles.name, data.name)).limit(1);
    if (existing.length) throw new BadRequestException('Role name already exists');
    const [role] = await this.db.insert(roles).values({
      name: data.name,
      description: data.description,
      isSystemRole: false,
    }).returning();
    return {
      id: role.id,
      name: role.name,
      description: role.description,
      isSystemRole: role.isSystemRole,
      createdAt: role.createdAt?.toISOString(),
      updatedAt: role.updatedAt?.toISOString(),
    };
  }

  async updateRole(roleId: string, data: UpdateRoleRequest): Promise<Role> {
    const [role] = await this.db.update(roles).set({
      name: data.name,
      description: data.description,
      updatedAt: new Date(),
    }).where(eq(roles.id, roleId)).returning();
    if (!role) throw new NotFoundException('Role not found');
    return {
      id: role.id,
      name: role.name,
      description: role.description,
      isSystemRole: role.isSystemRole,
      createdAt: role.createdAt?.toISOString(),
      updatedAt: role.updatedAt?.toISOString(),
    };
  }

  async deleteRole(roleId: string): Promise<{ message: string }> {
    const [deleted] = await this.db.delete(roles).where(eq(roles.id, roleId)).returning();
    if (!deleted) throw new NotFoundException('Role not found');
    return { message: 'Role deleted' };
  }

  async assignPermissionToRole(roleId: string, permissionId: string): Promise<{ message: string }> {
    // Check if already assigned
    const existing = await this.db.select().from(rolePermissions)
      .where(and(eq(rolePermissions.roleId, roleId), eq(rolePermissions.permissionId, permissionId))).limit(1);
    if (existing.length) throw new BadRequestException('Permission already assigned to role');
    // Check permission exists
    const [perm] = await this.db.select().from(permissions).where(eq(permissions.id, permissionId)).limit(1);
    if (!perm) throw new NotFoundException('Permission not found');
    await this.db.insert(rolePermissions).values({ roleId, permissionId }).returning();
    return { message: 'Permission assigned to role' };
  }

  async removePermissionFromRole(roleId: string, permissionId: string): Promise<{ message: string }> {
    const [deleted] = await this.db.delete(rolePermissions)
      .where(and(eq(rolePermissions.roleId, roleId), eq(rolePermissions.permissionId, permissionId))).returning();
    if (!deleted) throw new NotFoundException('Permission not assigned to role');
    return { message: 'Permission removed from role' };
  }
} 