import { permissions } from '@/database/schema';
import { BadRequestException, Inject, Injectable, NotFoundException } from '@nestjs/common';
import { eq } from 'drizzle-orm';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';

export interface PermissionDto {
  id: string;
  name: string;
  resource: string;
  action: string;
  description?: string;
  isSystemPermission: boolean;
  createdAt?: string;
}

@Injectable()
export class PermissionsService {
  constructor(@Inject('DB') private readonly db: PostgresJsDatabase<typeof import('@/database/schema')>) {}

  async listPermissions(): Promise<PermissionDto[]> {
    const dbPerms = await this.db.select().from(permissions);
    return dbPerms.map((p) => ({
      id: p.id,
      name: p.name,
      resource: p.resource,
      action: p.action,
      description: p.description,
      isSystemPermission: p.isSystemPermission,
      createdAt: p.createdAt?.toISOString(),
    }));
  }

  async getPermission(permissionId: string): Promise<PermissionDto> {
    const [perm] = await this.db.select().from(permissions).where(eq(permissions.id, permissionId)).limit(1);
    if (!perm) throw new NotFoundException('Permission not found');
    return {
      id: perm.id,
      name: perm.name,
      resource: perm.resource,
      action: perm.action,
      description: perm.description,
      isSystemPermission: perm.isSystemPermission,
      createdAt: perm.createdAt?.toISOString(),
    };
  }

  async createPermission(data: Omit<PermissionDto, 'id' | 'createdAt' | 'isSystemPermission'>): Promise<PermissionDto> {
    // Name must be unique
    const existing = await this.db.select().from(permissions).where(eq(permissions.name, data.name)).limit(1);
    if (existing.length) throw new BadRequestException('Permission name already exists');
    const [perm] = await this.db.insert(permissions).values({
      name: data.name,
      resource: data.resource,
      action: data.action,
      description: data.description,
      isSystemPermission: false,
    }).returning();
    return {
      id: perm.id,
      name: perm.name,
      resource: perm.resource,
      action: perm.action,
      description: perm.description,
      isSystemPermission: perm.isSystemPermission,
      createdAt: perm.createdAt?.toISOString(),
    };
  }

  async updatePermission(permissionId: string, data: Partial<Omit<PermissionDto, 'id' | 'createdAt' | 'isSystemPermission'>>): Promise<PermissionDto> {
    const [perm] = await this.db.update(permissions).set({
      name: data.name,
      resource: data.resource,
      action: data.action,
      description: data.description,
    }).where(eq(permissions.id, permissionId)).returning();
    if (!perm) throw new NotFoundException('Permission not found');
    return {
      id: perm.id,
      name: perm.name,
      resource: perm.resource,
      action: perm.action,
      description: perm.description,
      isSystemPermission: perm.isSystemPermission,
      createdAt: perm.createdAt?.toISOString(),
    };
  }

  async deletePermission(permissionId: string): Promise<{ message: string }> {
    const [deleted] = await this.db.delete(permissions).where(eq(permissions.id, permissionId)).returning();
    if (!deleted) throw new NotFoundException('Permission not found');
    return { message: 'Permission deleted' };
  }
} 