import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  UseGuards,
  HttpCode,
  HttpStatus,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../common/guards/jwt-auth.guard';
import { CurrentUser } from '../common/decorators/current-user.decorator';
import { DeveloperManagementService } from '../common/services/developer-management.service';
import { RepositorySyncService } from '../common/services/repository-sync.service';
import { GiteaProfileDto } from './dto/gitea-profile.dto';
import { DeveloperRepositoryDto } from './dto/developer-repository.dto';
import type { User } from '../database/schema';

export interface CreateDeveloperRequest {
  giteaUsername?: string;
  giteaPassword?: string;
  autoProvision?: boolean;
}

export interface ProvisionGiteaRequest {
  password?: string;
}

export interface SyncRepositoriesResponse {
  synced: number;
  created: number;
  updated: number;
  errors: string[];
}

@ApiTags('Developer Management')
@Controller('developers')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class DeveloperController {
  constructor(
    private readonly developerManagementService: DeveloperManagementService,
    private readonly repositorySyncService: RepositorySyncService,
  ) {}

  @Post('create')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ 
    summary: 'Create developer profile',
    description: 'Creates a developer profile and optionally provisions a Gitea account'
  })
  @ApiResponse({ 
    status: 201, 
    description: 'Developer profile created successfully',
    type: GiteaProfileDto
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 409, description: 'Developer profile already exists' })
  async createDeveloper(
    @CurrentUser() user: User,
    @Body() createDeveloperRequest: CreateDeveloperRequest,
  ): Promise<GiteaProfileDto> {
    try {
      const result = await this.developerManagementService.createDeveloper({
        userId: user.id,
        giteaUsername: createDeveloperRequest.giteaUsername,
        giteaPassword: createDeveloperRequest.giteaPassword,
        autoProvision: createDeveloperRequest.autoProvision ?? true,
      });

      return {
        id: result.giteaProfile?.id || '',
        userId: result.user.id,
        giteaUserId: result.giteaProfile?.giteaUserId || 0,
        giteaUsername: result.giteaProfile?.giteaUsername || '',
        giteaEmail: result.giteaProfile?.giteaEmail || '',
        giteaFullName: result.giteaProfile?.giteaFullName || '',
        giteaAvatarUrl: result.giteaProfile?.giteaAvatarUrl || null,
        isActive: result.giteaProfile?.isActive || false,
        isProvisioned: result.isProvisioned,
        syncStatus: result.giteaProfile?.syncStatus || 'pending',
        lastSyncAt: result.giteaProfile?.lastSyncAt || null,
        totalRepositories: result.repositoryCount,
        publicRepositories: result.giteaProfile?.publicRepositories || 0,
        privateRepositories: result.giteaProfile?.privateRepositories || 0,
        publishedRepositories: result.publishedCount,
        giteaProfile: result.giteaProfile?.giteaProfile || null,
        syncErrors: result.giteaProfile?.syncErrors || null,
        createdAt: result.giteaProfile?.createdAt || new Date(),
        updatedAt: result.giteaProfile?.updatedAt || new Date(),
      };
    } catch (error) {
      if (error.message.includes('already exists')) {
        throw new BadRequestException('Developer profile already exists for this user');
      }
      throw new BadRequestException(`Failed to create developer profile: ${error.message}`);
    }
  }

  @Get('profile')
  @ApiOperation({ 
    summary: 'Get developer profile',
    description: 'Retrieves the current user\'s developer profile'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Developer profile retrieved successfully',
    type: GiteaProfileDto
  })
  @ApiResponse({ status: 404, description: 'Developer profile not found' })
  async getDeveloperProfile(@CurrentUser() user: User): Promise<GiteaProfileDto> {
    const result = await this.developerManagementService.getDeveloperProfile(user.id);
    
    if (!result) {
      throw new NotFoundException('Developer profile not found');
    }

    return {
      id: result.giteaProfile?.id || '',
      userId: result.user.id,
      giteaUserId: result.giteaProfile?.giteaUserId || 0,
      giteaUsername: result.giteaProfile?.giteaUsername || '',
      giteaEmail: result.giteaProfile?.giteaEmail || '',
      giteaFullName: result.giteaProfile?.giteaFullName || '',
      giteaAvatarUrl: result.giteaProfile?.giteaAvatarUrl || null,
      isActive: result.giteaProfile?.isActive || false,
      isProvisioned: result.isProvisioned,
      syncStatus: result.giteaProfile?.syncStatus || 'pending',
      lastSyncAt: result.giteaProfile?.lastSyncAt || null,
      totalRepositories: result.repositoryCount,
      publicRepositories: result.giteaProfile?.publicRepositories || 0,
      privateRepositories: result.giteaProfile?.privateRepositories || 0,
      publishedRepositories: result.publishedCount,
      giteaProfile: result.giteaProfile?.giteaProfile || null,
      syncErrors: result.giteaProfile?.syncErrors || null,
      createdAt: result.giteaProfile?.createdAt || new Date(),
      updatedAt: result.giteaProfile?.updatedAt || new Date(),
    };
  }

  @Post('provision')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Provision Gitea account',
    description: 'Provisions a Gitea account for an existing developer profile'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Gitea account provisioned successfully',
    type: GiteaProfileDto
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'Developer profile not found' })
  @ApiResponse({ status: 409, description: 'Gitea account already provisioned' })
  async provisionGiteaAccount(
    @CurrentUser() user: User,
    @Body() provisionRequest: ProvisionGiteaRequest,
  ): Promise<GiteaProfileDto> {
    try {
      const result = await this.developerManagementService.provisionGiteaAccount(
        user.id,
        provisionRequest.password,
      );

      return {
        id: result.giteaProfile?.id || '',
        userId: result.user.id,
        giteaUserId: result.giteaProfile?.giteaUserId || 0,
        giteaUsername: result.giteaProfile?.giteaUsername || '',
        giteaEmail: result.giteaProfile?.giteaEmail || '',
        giteaFullName: result.giteaProfile?.giteaFullName || '',
        giteaAvatarUrl: result.giteaProfile?.giteaAvatarUrl || null,
        isActive: result.giteaProfile?.isActive || false,
        isProvisioned: result.isProvisioned,
        syncStatus: result.giteaProfile?.syncStatus || 'pending',
        lastSyncAt: result.giteaProfile?.lastSyncAt || null,
        totalRepositories: result.repositoryCount,
        publicRepositories: result.giteaProfile?.publicRepositories || 0,
        privateRepositories: result.giteaProfile?.privateRepositories || 0,
        publishedRepositories: result.publishedCount,
        giteaProfile: result.giteaProfile?.giteaProfile || null,
        syncErrors: result.giteaProfile?.syncErrors || null,
        createdAt: result.giteaProfile?.createdAt || new Date(),
        updatedAt: result.giteaProfile?.updatedAt || new Date(),
      };
    } catch (error) {
      if (error.message.includes('not found')) {
        throw new NotFoundException('Developer profile not found');
      }
      if (error.message.includes('already provisioned')) {
        throw new BadRequestException('Gitea account already provisioned');
      }
      throw new BadRequestException(`Failed to provision Gitea account: ${error.message}`);
    }
  }

  @Post('sync')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Sync developer profile',
    description: 'Syncs the developer profile with Gitea data'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Developer profile synced successfully',
    type: GiteaProfileDto
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'Developer profile not found' })
  async syncDeveloperProfile(@CurrentUser() user: User): Promise<GiteaProfileDto> {
    try {
      const result = await this.developerManagementService.syncDeveloperProfile(user.id);

      return {
        id: result.giteaProfile?.id || '',
        userId: result.user.id,
        giteaUserId: result.giteaProfile?.giteaUserId || 0,
        giteaUsername: result.giteaProfile?.giteaUsername || '',
        giteaEmail: result.giteaProfile?.giteaEmail || '',
        giteaFullName: result.giteaProfile?.giteaFullName || '',
        giteaAvatarUrl: result.giteaProfile?.giteaAvatarUrl || null,
        isActive: result.giteaProfile?.isActive || false,
        isProvisioned: result.isProvisioned,
        syncStatus: result.giteaProfile?.syncStatus || 'pending',
        lastSyncAt: result.giteaProfile?.lastSyncAt || null,
        totalRepositories: result.repositoryCount,
        publicRepositories: result.giteaProfile?.publicRepositories || 0,
        privateRepositories: result.giteaProfile?.privateRepositories || 0,
        publishedRepositories: result.publishedCount,
        giteaProfile: result.giteaProfile?.giteaProfile || null,
        syncErrors: result.giteaProfile?.syncErrors || null,
        createdAt: result.giteaProfile?.createdAt || new Date(),
        updatedAt: result.giteaProfile?.updatedAt || new Date(),
      };
    } catch (error) {
      if (error.message.includes('not found')) {
        throw new NotFoundException('Provisioned developer profile not found');
      }
      throw new BadRequestException(`Failed to sync developer profile: ${error.message}`);
    }
  }

  @Get('repositories')
  @ApiOperation({ 
    summary: 'Get developer repositories',
    description: 'Retrieves all repositories for the current developer'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Repositories retrieved successfully',
    type: [DeveloperRepositoryDto]
  })
  @ApiResponse({ status: 404, description: 'Developer profile not found' })
  async getDeveloperRepositories(@CurrentUser() user: User): Promise<DeveloperRepositoryDto[]> {
    try {
      const repositories = await this.repositorySyncService.getDeveloperRepositories(user.id, true);
      
      return repositories.map(repo => ({
        id: repo.id,
        giteaRepoId: repo.giteaRepoId,
        name: repo.name,
        fullName: repo.fullName,
        description: repo.description,
        visibility: repo.visibility as 'public' | 'private',
        isFork: repo.isFork,
        isTemplate: repo.isTemplate,
        isArchived: repo.isArchived,
        isEmpty: repo.isEmpty,
        size: repo.size,
        starsCount: repo.starsCount,
        forksCount: repo.forksCount,
        watchersCount: repo.watchersCount,
        openIssuesCount: repo.openIssuesCount,
        defaultBranch: repo.defaultBranch,
        language: repo.language,
        topics: repo.topics || [],
        htmlUrl: repo.htmlUrl,
        cloneUrl: repo.cloneUrl,
        sshUrl: repo.sshUrl,
        isPublished: repo.isPublished || false,
        marketplaceItemId: repo.marketplaceItemId,
        hasMarketplaceMetadata: repo.hasMarketplaceMetadata || false,
        marketplaceMetadata: repo.marketplaceMetadata,
        syncStatus: repo.syncStatus,
        lastSyncAt: repo.lastSyncAt,
        giteaCreatedAt: repo.giteaCreatedAt,
        giteaUpdatedAt: repo.giteaUpdatedAt,
        giteaPushedAt: repo.giteaPushedAt,
        createdAt: repo.createdAt,
        updatedAt: repo.updatedAt,
      }));
    } catch (error) {
      if (error.message.includes('not found')) {
        throw new NotFoundException('Developer profile not found');
      }
      throw new BadRequestException(`Failed to get repositories: ${error.message}`);
    }
  }

  @Post('repositories/sync')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Sync repositories',
    description: 'Syncs all repositories for the current developer with Gitea'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Repositories synced successfully'
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'Developer profile not found' })
  async syncRepositories(@CurrentUser() user: User): Promise<SyncRepositoriesResponse> {
    try {
      const result = await this.repositorySyncService.syncDeveloperRepositories(user.id);
      return result;
    } catch (error) {
      if (error.message.includes('not found')) {
        throw new NotFoundException('Developer profile not found');
      }
      if (error.message.includes('not provisioned')) {
        throw new BadRequestException('Developer profile not provisioned with Gitea');
      }
      throw new BadRequestException(`Failed to sync repositories: ${error.message}`);
    }
  }

  @Get('repositories/:id')
  @ApiOperation({ 
    summary: 'Get repository details',
    description: 'Retrieves details for a specific repository'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Repository details retrieved successfully',
    type: DeveloperRepositoryDto
  })
  @ApiResponse({ status: 404, description: 'Repository not found' })
  async getRepository(@Param('id') repositoryId: string): Promise<DeveloperRepositoryDto> {
    try {
      const repo = await this.repositorySyncService.getRepository(repositoryId);
      
      return {
        id: repo.id,
        giteaRepoId: repo.giteaRepoId,
        name: repo.name,
        fullName: repo.fullName,
        description: repo.description,
        visibility: repo.visibility as 'public' | 'private',
        isFork: repo.isFork,
        isTemplate: repo.isTemplate,
        isArchived: repo.isArchived,
        isEmpty: repo.isEmpty,
        size: repo.size,
        starsCount: repo.starsCount,
        forksCount: repo.forksCount,
        watchersCount: repo.watchersCount,
        openIssuesCount: repo.openIssuesCount,
        defaultBranch: repo.defaultBranch,
        language: repo.language,
        topics: repo.topics || [],
        htmlUrl: repo.htmlUrl,
        cloneUrl: repo.cloneUrl,
        sshUrl: repo.sshUrl,
        isPublished: repo.isPublished || false,
        marketplaceItemId: repo.marketplaceItemId,
        hasMarketplaceMetadata: repo.hasMarketplaceMetadata || false,
        marketplaceMetadata: repo.marketplaceMetadata,
        syncStatus: repo.syncStatus,
        lastSyncAt: repo.lastSyncAt,
        giteaCreatedAt: repo.giteaCreatedAt,
        giteaUpdatedAt: repo.giteaUpdatedAt,
        giteaPushedAt: repo.giteaPushedAt,
        createdAt: repo.createdAt,
        updatedAt: repo.updatedAt,
      };
    } catch (error) {
      if (error.message.includes('not found')) {
        throw new NotFoundException('Repository not found');
      }
      throw new BadRequestException(`Failed to get repository: ${error.message}`);
    }
  }
}
