import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEmail, IsBoolean, IsNumber, IsArray } from 'class-validator';

export class GiteaProfile {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  id?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  username?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  fullName?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @IsEmail()
  email?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  avatarUrl?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  profileUrl?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  publicRepos?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  privateRepos?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  totalRepos?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  followers?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  following?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  provisionedAt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  lastSyncAt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  syncStatus?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  accountStatus?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  ssoEnabled?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  adminProvisioned?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  provisionedBy?: string;

}
