import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNotEmpty, IsString, IsEmail, IsBoolean, IsNumber, IsArray } from 'class-validator';

export class DeveloperRepository {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  id?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  fullName?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  private?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  fork?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  size?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  language?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  stars?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  forks?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  openIssues?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  defaultBranch?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  createdAt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  updatedAt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  pushedAt?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  cloneUrl?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  sshUrl?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  htmlUrl?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isPublished?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  marketplaceItemId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  hasMarketplaceMetadata?: boolean;

}
