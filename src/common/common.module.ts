import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule } from '../database/database.module';
import { ClientReleasesService } from './services/client-releases.service';
import { FileUploadsService } from './services/file-uploads.service';
import { S3Service } from './services/s3.service';

@Module({
  imports: [ConfigModule, DatabaseModule],
  providers: [S3Service, ClientReleasesService, FileUploadsService],
  exports: [S3Service, ClientReleasesService, FileUploadsService],
})
export class CommonModule {}