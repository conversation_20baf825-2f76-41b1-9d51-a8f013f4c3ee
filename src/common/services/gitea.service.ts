import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import {
  GiteaVersionResponseDto
} from '../dto/gitea-response.dto';

export interface GiteaUser {
  id: number;
  login: string;
  full_name: string;
  email: string;
  avatar_url: string;
  language: string;
  is_admin: boolean;
  last_login: string;
  created: string;
  restricted: boolean;
  active: boolean;
  prohibit_login: boolean;
  location: string;
  website: string;
  description: string;
  visibility: string;
  followers_count: number;
  following_count: number;
  starred_repos_count: number;
  username: string;
}

export interface GiteaRepository {
  id: number;
  name: string;
  full_name: string;
  description: string;
  empty: boolean;
  private: boolean;
  fork: boolean;
  template: boolean;
  parent: any;
  mirror: boolean;
  size: number;
  language: string;
  languages_url: string;
  html_url: string;
  ssh_url: string;
  clone_url: string;
  original_url: string;
  website: string;
  stars_count: number;
  forks_count: number;
  watchers_count: number;
  open_issues_count: number;
  open_pr_counter: number;
  release_counter: number;
  default_branch: string;
  archived: boolean;
  created_at: string;
  updated_at: string;
  pushed_at: string;
  permissions: {
    admin: boolean;
    push: boolean;
    pull: boolean;
  };
  has_issues: boolean;
  internal_tracker: {
    enable_time_tracker: boolean;
    allow_only_contributors_to_track_time: boolean;
    enable_issue_dependencies: boolean;
  };
  has_wiki: boolean;
  has_pull_requests: boolean;
  has_projects: boolean;
  ignore_whitespace_conflicts: boolean;
  allow_merge_commits: boolean;
  allow_rebase: boolean;
  allow_rebase_explicit: boolean;
  allow_squash_merge: boolean;
  default_merge_style: string;
  avatar_url: string;
  internal: boolean;
  mirror_interval: string;
  mirror_updated: string;
  repo_transfer: any;
}

export interface CreateUserOptions {
  username: string;
  email: string;
  password: string;
  full_name?: string;
  login_name?: string;
  send_notify?: boolean;
  source_id?: number;
  must_change_password?: boolean;
  restricted?: boolean;
  visibility?: 'public' | 'limited' | 'private';
}

export interface UpdateUserOptions {
  source_id?: number;
  login_name?: string;
  full_name?: string;
  email?: string;
  password?: string;
  must_change_password?: boolean;
  website?: string;
  location?: string;
  description?: string;
  active?: boolean;
  admin?: boolean;
  allow_git_hook?: boolean;
  allow_import_local?: boolean;
  max_repo_creation?: number;
  prohibit_login?: boolean;
  allow_create_organization?: boolean;
  restricted?: boolean;
  visibility?: 'public' | 'limited' | 'private';
}

export interface CreateRepositoryOptions {
  name: string;
  description?: string;
  private?: boolean;
  auto_init?: boolean;
  gitignores?: string;
  license?: string;
  readme?: string;
  default_branch?: string;
  trust_model?: 'default' | 'collaborator' | 'committer' | 'collaboratorcommitter';
}

export interface WebhookOptions {
  type: 'gitea' | 'gogs' | 'slack' | 'discord' | 'dingtalk' | 'telegram' | 'msteams' | 'feishu' | 'wechatwork' | 'packagist';
  config: {
    url: string;
    content_type?: 'json' | 'form';
    secret?: string;
    username?: string;
    password?: string;
    http_method?: string;
  };
  events: string[];
  active?: boolean;
  branch_filter?: string;
}

@Injectable()
export class GiteaService {
  private readonly logger = new Logger(GiteaService.name);
  private readonly client: ReturnType<typeof axios.create>;
  private readonly baseUrl: string;
  private readonly externalUrl: string;
  private readonly adminToken: string;

  constructor(private configService: ConfigService) {
    this.baseUrl = this.configService.get<string>('GITEA_BASE_URL');
    this.externalUrl = this.configService.get<string>('GITEA_EXTERNAL_URL');
    this.adminToken = this.configService.get<string>('GITEA_ADMIN_TOKEN');

    if (!this.baseUrl || !this.adminToken) {
      throw new Error('Gitea configuration missing: GITEA_BASE_URL and GITEA_ADMIN_TOKEN are required');
    }

    this.client = axios.create({
      baseURL: `${this.baseUrl}/api/v1`,
      headers: {
        'Authorization': `token ${this.adminToken}`,
        'Content-Type': 'application/json',
      },
      timeout: 30000,
    });

    this.logger.log(`Gitea Service initialized with base URL: ${this.baseUrl}`);
    this.logger.log(`Gitea Service external URL: ${this.externalUrl}`);
  }

  /**
   * Test connection to Gitea
   */
  async testConnection(): Promise<boolean> {
    try {
      const response = await this.client.get('/version');
      this.logger.log(`Gitea connection successful. Version: ${(response.data as GiteaVersionResponseDto).version}`);
      return true;
    } catch (error) {
      this.logger.error('Failed to connect to Gitea:', error.message);
      return false;
    }
  }

  /**
   * Get Gitea version information
   */
  async getVersion(): Promise<GiteaVersionResponseDto> {
    try {
      const response = await this.client.get('/version');
      return response.data;
    } catch (error) {
      this.logger.error('Failed to get Gitea version:', error.message);
      throw new BadRequestException('Failed to connect to Gitea');
    }
  }

  // User Management Methods
  /**
   * Create a new user in Gitea
   */
  async createUser(options: CreateUserOptions): Promise<GiteaUser> {
    try {
      const response = await this.client.post('/admin/users', {
        username: options.username,
        email: options.email,
        password: options.password,
        full_name: options.full_name || options.username,
        login_name: options.login_name || options.username,
        send_notify: options.send_notify || false,
        source_id: options.source_id || 0,
        must_change_password: options.must_change_password || false,
        restricted: options.restricted || false,
        visibility: options.visibility || 'public',
      });

      this.logger.log(`Created Gitea user: ${options.username} (ID: ${(response.data as any).id})`);
      return response.data as GiteaUser;
    } catch (error) {
      this.logger.error(`Failed to create Gitea user ${options.username}:`, error.response?.data || error.message);
      throw new BadRequestException(`Failed to create Gitea user: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Get user by username
   */
  async getUserByUsername(username: string): Promise<GiteaUser> {
    try {
      const response = await this.client.get(`/users/${username}`);
      return response.data as GiteaUser;
    } catch (error) {
      if (error.response?.status === 404) {
        throw new NotFoundException(`Gitea user not found: ${username}`);
      }
      this.logger.error(`Failed to get Gitea user ${username}:`, error.response?.data || error.message);
      throw new BadRequestException(`Failed to get Gitea user: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Update user information
   */
  async updateUser(username: string, options: UpdateUserOptions): Promise<GiteaUser> {
    try {
      const response = await this.client.patch(`/admin/users/${username}`, options);
      this.logger.log(`Updated Gitea user: ${username}`);
      return response.data as GiteaUser;
    } catch (error) {
      this.logger.error(`Failed to update Gitea user ${username}:`, error.response?.data || error.message);
      throw new BadRequestException(`Failed to update Gitea user: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Delete user
   */
  async deleteUser(username: string): Promise<void> {
    try {
      await this.client.delete(`/admin/users/${username}`);
      this.logger.log(`Deleted Gitea user: ${username}`);
    } catch (error) {
      this.logger.error(`Failed to delete Gitea user ${username}:`, error.response?.data || error.message);
      throw new BadRequestException(`Failed to delete Gitea user: ${error.response?.data?.message || error.message}`);
    }
  }

  // Repository Management Methods
  /**
   * Get user repositories
   */
  async getUserRepositories(username: string, page: number = 1, limit: number = 50): Promise<GiteaRepository[]> {
    try {
      const response = await this.client.get(`/users/${username}/repos`, {
        params: { page, limit }
      });
      return response.data as GiteaRepository[];
    } catch (error) {
      this.logger.error(`Failed to get repositories for user ${username}:`, error.response?.data || error.message);
      throw new BadRequestException(`Failed to get repositories: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Get repository by owner and name
   */
  async getRepository(owner: string, repo: string): Promise<GiteaRepository> {
    try {
      const response = await this.client.get(`/repos/${owner}/${repo}`);
      return response.data as GiteaRepository;
    } catch (error) {
      if (error.response?.status === 404) {
        throw new NotFoundException(`Repository not found: ${owner}/${repo}`);
      }
      this.logger.error(`Failed to get repository ${owner}/${repo}:`, error.response?.data || error.message);
      throw new BadRequestException(`Failed to get repository: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Create repository for user
   */
  async createRepository(username: string, options: CreateRepositoryOptions): Promise<GiteaRepository> {
    try {
      const response = await this.client.post(`/admin/users/${username}/repos`, {
        name: options.name,
        description: options.description || '',
        private: options.private || false,
        auto_init: options.auto_init || true,
        gitignores: options.gitignores || '',
        license: options.license || '',
        readme: options.readme || 'Default',
        default_branch: options.default_branch || 'main',
        trust_model: options.trust_model || 'default',
      });

      this.logger.log(`Created repository: ${username}/${options.name}`);
      return response.data as GiteaRepository;
    } catch (error) {
      this.logger.error(`Failed to create repository ${username}/${options.name}:`, error.response?.data || error.message);
      throw new BadRequestException(`Failed to create repository: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Delete repository
   */
  async deleteRepository(owner: string, repo: string): Promise<void> {
    try {
      await this.client.delete(`/repos/${owner}/${repo}`);
      this.logger.log(`Deleted repository: ${owner}/${repo}`);
    } catch (error) {
      this.logger.error(`Failed to delete repository ${owner}/${repo}:`, error.response?.data || error.message);
      throw new BadRequestException(`Failed to delete repository: ${error.response?.data?.message || error.message}`);
    }
  }

  // Webhook Management Methods
  /**
   * Create webhook for repository
   */
  async createWebhook(owner: string, repo: string, options: WebhookOptions): Promise<any> {
    try {
      const response = await this.client.post(`/repos/${owner}/${repo}/hooks`, {
        type: options.type,
        config: options.config,
        events: options.events,
        active: options.active !== false,
        branch_filter: options.branch_filter || '',
      });

      this.logger.log(`Created webhook for repository: ${owner}/${repo}`);
      return response.data;
    } catch (error) {
      this.logger.error(`Failed to create webhook for ${owner}/${repo}:`, error.response?.data || error.message);
      throw new BadRequestException(`Failed to create webhook: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Get repository webhooks
   */
  async getWebhooks(owner: string, repo: string): Promise<any[]> {
    try {
      const response = await this.client.get(`/repos/${owner}/${repo}/hooks`);
      return response.data as any[];
    } catch (error) {
      this.logger.error(`Failed to get webhooks for ${owner}/${repo}:`, error.response?.data || error.message);
      throw new BadRequestException(`Failed to get webhooks: ${error.response?.data?.message || error.message}`);
    }
  }

  /**
   * Delete webhook
   */
  async deleteWebhook(owner: string, repo: string, hookId: number): Promise<void> {
    try {
      await this.client.delete(`/repos/${owner}/${repo}/hooks/${hookId}`);
      this.logger.log(`Deleted webhook ${hookId} for repository: ${owner}/${repo}`);
    } catch (error) {
      this.logger.error(`Failed to delete webhook ${hookId} for ${owner}/${repo}:`, error.response?.data || error.message);
      throw new BadRequestException(`Failed to delete webhook: ${error.response?.data?.message || error.message}`);
    }
  }

  // Utility Methods
  /**
   * Get external URL for repository
   */
  getRepositoryExternalUrl(owner: string, repo: string): string {
    return `${this.externalUrl}/${owner}/${repo}`;
  }

  /**
   * Get clone URL for repository
   */
  getRepositoryCloneUrl(owner: string, repo: string, useSSH: boolean = false): string {
    if (useSSH) {
      const domain = this.externalUrl.replace(/^https?:\/\//, '');
      return `git@${domain}:${owner}/${repo}.git`;
    }
    return `${this.externalUrl}/${owner}/${repo}.git`;
  }
}
