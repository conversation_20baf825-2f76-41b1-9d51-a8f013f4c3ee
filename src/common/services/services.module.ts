import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DatabaseService } from './database.service';
import { RedisService } from './redis.service';
import { S3Service } from './s3.service';
import { FileUploadsService } from './file-uploads.service';
import { ClientReleasesService } from './client-releases.service';
import { GiteaService } from './gitea.service';
import { DeveloperManagementService } from './developer-management.service';
import { RepositorySyncService } from './repository-sync.service';
import { WebhookProcessorService } from './webhook-processor.service';

@Module({
  imports: [ConfigModule],
  providers: [
    DatabaseService,
    RedisService,
    S3Service,
    FileUploadsService,
    ClientReleasesService,
    GiteaService,
    DeveloperManagementService,
    RepositorySyncService,
    WebhookProcessorService,
  ],
  exports: [
    DatabaseService,
    RedisService,
    S3Service,
    FileUploadsService,
    ClientReleasesService,
    GiteaService,
    DeveloperManagementService,
    RepositorySyncService,
    WebhookProcessorService,
  ],
})
export class ServicesModule {}
