import { Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { and, desc, eq } from 'drizzle-orm';
import { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import * as schema from '../../database/schema';
import { ClientRelease, clientReleases, NewClientRelease } from '../../database/schema/client-releases.schema';
import { S3Service } from './s3.service';

export interface TauriUpdateResponse {
  version: string;
  notes: string;
  pub_date: string;
  url: string;
  signature: string;
}

export interface CheckUpdateRequest {
  target: string; // e.g., "windows", "macos", "linux"
  arch: string;   // e.g., "x64", "arm64"
  current_version: string;
  channel?: 'stable' | 'beta';
}

@Injectable()
export class ClientReleasesService {
  private readonly logger = new Logger(ClientReleasesService.name);

  constructor(
    @Inject('DB') private db: PostgresJsDatabase<typeof schema>,
    private s3Service: S3Service,
  ) {}

  /**
   * Check for updates - main endpoint for Tauri updater
   */
  async checkForUpdate(request: CheckUpdateRequest): Promise<TauriUpdateResponse | null> {
    const { target, arch, current_version, channel = 'stable' } = request;

    try {
      // Find the latest active release for the platform/arch/channel
      const latestRelease = await this.db
        .select()
        .from(clientReleases)
        .where(
          and(
            eq(clientReleases.targetPlatform, target as any),
            eq(clientReleases.targetArch, arch as any),
            eq(clientReleases.channel, channel),
            eq(clientReleases.isActive, true)
          )
        )
        .orderBy(desc(clientReleases.pubDate))
        .limit(1);

      if (!latestRelease.length) {
        this.logger.log(`No releases found for ${target}/${arch}/${channel}`);
        return null;
      }

      const release = latestRelease[0];

      // Check if update is needed (simple version comparison)
      if (this.isVersionNewer(release.version, current_version)) {
        // Generate signed download URL
        const downloadUrl = await this.s3Service.getPresignedDownloadUrl({
          key: release.s3Key,
          bucket: release.s3Bucket,
          expires: 3600, // 1 hour
        });

        this.logger.log(`Update available: ${current_version} -> ${release.version}`);

        return {
          version: release.version,
          notes: release.notes || '',
          pub_date: release.pubDate.toISOString(),
          url: downloadUrl,
          signature: release.signature,
        };
      }

      this.logger.log(`No update needed for ${current_version}`);
      return null;
    } catch (error) {
      this.logger.error('Failed to check for updates', error);
      throw error;
    }
  }

  /**
   * Create a new release
   */
  async createRelease(releaseData: NewClientRelease): Promise<ClientRelease> {
    try {
      const [newRelease] = await this.db
        .insert(clientReleases)
        .values(releaseData)
        .returning();

      this.logger.log(`Created new release: ${newRelease.version}`);
      return newRelease;
    } catch (error) {
      this.logger.error('Failed to create release', error);
      throw error;
    }
  }

  /**
   * Activate a release (and deactivate others for the same platform/arch/channel)
   */
  async activateRelease(releaseId: string): Promise<void> {
    try {
      const release = await this.db
        .select()
        .from(clientReleases)
        .where(eq(clientReleases.id, releaseId))
        .limit(1);

      if (!release.length) {
        throw new NotFoundException('Release not found');
      }

      const targetRelease = release[0];

      // Deactivate all other releases for the same platform/arch/channel
      await this.db
        .update(clientReleases)
        .set({ isActive: false })
        .where(
          and(
            eq(clientReleases.targetPlatform, targetRelease.targetPlatform),
            eq(clientReleases.targetArch, targetRelease.targetArch),
            eq(clientReleases.channel, targetRelease.channel)
          )
        );

      // Activate the target release
      await this.db
        .update(clientReleases)
        .set({ isActive: true })
        .where(eq(clientReleases.id, releaseId));

      this.logger.log(`Activated release: ${targetRelease.version}`);
    } catch (error) {
      this.logger.error('Failed to activate release', error);
      throw error;
    }
  }

  /**
   * Deactivate a release
   */
  async deactivateRelease(releaseId: string): Promise<void> {
    try {
      await this.db
        .update(clientReleases)
        .set({ isActive: false })
        .where(eq(clientReleases.id, releaseId));

      this.logger.log(`Deactivated release: ${releaseId}`);
    } catch (error) {
      this.logger.error('Failed to deactivate release', error);
      throw error;
    }
  }

  /**
   * Get all releases for a platform/arch/channel
   */
  async getReleases(
    platform?: string,
    arch?: string,
    channel?: 'stable' | 'beta'
  ): Promise<ClientRelease[]> {
    try {
      const conditions = [];
      if (platform) conditions.push(eq(clientReleases.targetPlatform, platform as any));
      if (arch) conditions.push(eq(clientReleases.targetArch, arch as any));
      if (channel) conditions.push(eq(clientReleases.channel, channel));

      let query = this.db.select().from(clientReleases);

      if (conditions.length > 0) {
        query = query.where(and(...conditions));
      }

      const releases = await query.orderBy(desc(clientReleases.pubDate));
      return releases;
    } catch (error) {
      this.logger.error('Failed to get releases', error);
      throw error;
    }
  }

  /**
   * Get active release for platform/arch/channel
   */
  async getActiveRelease(
    platform: string,
    arch: string,
    channel: 'stable' | 'beta' = 'stable'
  ): Promise<ClientRelease | null> {
    try {
      const releases = await this.db
        .select()
        .from(clientReleases)
        .where(
          and(
            eq(clientReleases.targetPlatform, platform as any),
            eq(clientReleases.targetArch, arch as any),
            eq(clientReleases.channel, channel),
            eq(clientReleases.isActive, true)
          )
        )
        .limit(1);

      return releases.length > 0 ? releases[0] : null;
    } catch (error) {
      this.logger.error('Failed to get active release', error);
      throw error;
    }
  }

  /**
   * Delete a release and its associated file
   */
  async deleteRelease(releaseId: string): Promise<void> {
    try {
      const release = await this.db
        .select()
        .from(clientReleases)
        .where(eq(clientReleases.id, releaseId))
        .limit(1);

      if (!release.length) {
        throw new NotFoundException('Release not found');
      }

      const targetRelease = release[0];

      // Delete file from S3
      await this.s3Service.deleteFile(targetRelease.s3Key, targetRelease.s3Bucket);

      // Delete release record
      await this.db
        .delete(clientReleases)
        .where(eq(clientReleases.id, releaseId));

      this.logger.log(`Deleted release: ${targetRelease.version}`);
    } catch (error) {
      this.logger.error('Failed to delete release', error);
      throw error;
    }
  }

  /**
   * Simple version comparison (semantic versioning)
   */
  private isVersionNewer(newVersion: string, currentVersion: string): boolean {
    // Remove 'v' prefix if present
    const cleanNew = newVersion.replace(/^v/, '');
    const cleanCurrent = currentVersion.replace(/^v/, '');

    const newParts = cleanNew.split('.').map(Number);
    const currentParts = cleanCurrent.split('.').map(Number);

    // Pad arrays to same length
    const maxLength = Math.max(newParts.length, currentParts.length);
    while (newParts.length < maxLength) newParts.push(0);
    while (currentParts.length < maxLength) currentParts.push(0);

    for (let i = 0; i < maxLength; i++) {
      if (newParts[i] > currentParts[i]) return true;
      if (newParts[i] < currentParts[i]) return false;
    }

    return false; // Versions are equal
  }
}
