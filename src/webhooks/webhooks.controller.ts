import {
  Controller,
  Post,
  Body,
  Headers,
  HttpCode,
  HttpStatus,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiHeader } from '@nestjs/swagger';
import { Public } from '../common/decorators/public.decorator';
import { WebhookProcessorService } from '../common/services/webhook-processor.service';
import { GiteaWebhookPayloadDto } from '../developer/dto/gitea-webhook-payload.dto';

export interface WebhookResponse {
  processed: boolean;
  action: string;
  repository?: string;
  message: string;
}

@ApiTags('Webhooks')
@Controller('webhooks')
@Public() // Webhooks don't use JWT authentication
export class WebhooksController {
  private readonly logger = new Logger(WebhooksController.name);

  constructor(
    private readonly webhookProcessorService: WebhookProcessorService,
  ) {}

  @Post('gitea')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Handle Gitea webhooks',
    description: 'Processes incoming webhooks from Gitea for repository events'
  })
  @ApiHeader({
    name: 'X-Gitea-Signature',
    description: 'Webhook signature for verification',
    required: false,
  })
  @ApiHeader({
    name: 'X-Gitea-Event',
    description: 'Type of Gitea event',
    required: true,
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Webhook processed successfully'
  })
  @ApiResponse({ status: 400, description: 'Invalid webhook payload or signature' })
  async handleGiteaWebhook(
    @Body() payload: any,
    @Headers('x-gitea-signature') signature?: string,
    @Headers('x-gitea-event') eventType?: string,
  ): Promise<WebhookResponse> {
    this.logger.log(`Received Gitea webhook: ${eventType}`);
    
    try {
      // Verify webhook signature if provided
      if (signature) {
        const isValid = this.webhookProcessorService.verifyWebhookSignature(
          JSON.stringify(payload),
          signature,
        );
        
        if (!isValid) {
          this.logger.error('Webhook signature verification failed');
          throw new BadRequestException('Invalid webhook signature');
        }
      }

      // Process different types of webhooks
      let result: WebhookResponse;

      switch (eventType) {
        case 'repository':
          result = await this.webhookProcessorService.processRepositoryWebhook(payload);
          break;
          
        case 'push':
          result = await this.webhookProcessorService.processPushWebhook(payload);
          break;
          
        case 'release':
          result = await this.webhookProcessorService.processReleaseWebhook(payload);
          break;
          
        default:
          this.logger.warn(`Unhandled webhook event type: ${eventType}`);
          result = {
            processed: false,
            action: eventType || 'unknown',
            message: `Unhandled event type: ${eventType}`,
          };
      }

      this.logger.log(`Webhook processed: ${result.processed ? 'success' : 'failed'} - ${result.message}`);
      return result;
      
    } catch (error) {
      this.logger.error(`Webhook processing failed: ${error.message}`, error.stack);
      
      if (error instanceof BadRequestException) {
        throw error;
      }
      
      throw new BadRequestException(`Webhook processing failed: ${error.message}`);
    }
  }

  @Post('gitea/test')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Test Gitea webhook endpoint',
    description: 'Test endpoint for verifying webhook connectivity'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Test webhook received successfully'
  })
  async testGiteaWebhook(@Body() payload: any): Promise<{ message: string; received: any }> {
    this.logger.log('Received test webhook from Gitea');
    
    return {
      message: 'Test webhook received successfully',
      received: {
        timestamp: new Date().toISOString(),
        payload: payload,
      },
    };
  }
}
