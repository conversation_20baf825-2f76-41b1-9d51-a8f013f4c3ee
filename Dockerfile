# Development Dockerfile for RSGlider API
FROM node:22-alpine

# Install system dependencies including build tools for native modules
RUN apk add --no-cache \
    curl \
    python3 \
    make \
    g++ \
    linux-headers \
    git \
    bash

# Create a non-root user
RUN addgroup -g 1000 node && adduser -u 1000 -G node -s /bin/sh -D node

# Create app directory and set ownership
WORKDIR /app
RUN chown -R node:node /app

# Switch to node user
USER node

# Copy package files first for better caching
COPY --chown=node:node package.json package-lock.json ./

# Install dependencies (this will be cached if package.json doesn't change)
RUN npm ci --only=production=false

# Copy source code (excluding node_modules via .dockerignore)
COPY --chown=node:node . .

# Rebuild native modules to ensure compatibility with container architecture
RUN npm rebuild

# Create uploads directory
RUN mkdir -p uploads

# Expose port and debug port
EXPOSE 3000 9229

# Default command for development
CMD ["npm", "run", "start:dev"]
